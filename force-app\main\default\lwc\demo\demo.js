import { LightningElement, api } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { loadScript, loadStyle } from 'lightning/platformResourceLoader';
import mobiscroll from '@salesforce/resourceUrl/mobiscroll';
export default class Demo extends LightningElement {

    @api calendar;

    renderedCallback(){
        console.log('calendar', this.calendar);
    }

    // static renderMode = 'light'; // the default is 'shadow'
    // mobiscrollInitialized=false;

    // renderedCallback(){
    //     if(this.mobiscrollInitialized) 
    //         return ;
    //     this.mobiscrollInitialized=true;

    //     Promise.all([
    //         loadScript(this, mobiscroll + '/js/mobiscroll.javascript.min.js'),
    //         loadStyle(this, mobiscroll + '/css/mobiscroll.javascript.min.css')
    //     ]).then(() => {
    //         this.initializeMobiscroll();
    //     })
    //     .catch(error => {
    //         this.dispatchEvent(
    //             new ShowToastEvent({
    //                 title: 'Error loading mobiscroll',
    //                 message: error.message,
    //                 variant: 'error'
    //             })
    //         );
    //     });

    // }

    // initializeMobiscroll(){
    //     var meta = document.createElement('meta');
    //     meta.httpEquiv = 'Content-Security-Policy';
    //     meta.content = "default-src 'unsafe-eval'; script-src 'unsafe-eval'";
    //     document.getElementsByTagName('head')[0].appendChild(meta);
    //     calendar = window.mobiscroll.eventCalendar(this.querySelector('#eventCalendar'), {
    //         clickToCreate: "double",
    //         dragToCreate:true,
    //         view: {
    //             schedule: { type: "week", size: 2 },
    //           }
    //     })
    // }
}